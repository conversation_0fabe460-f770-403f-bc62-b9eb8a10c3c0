'use client'
import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { getSupplier, isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import Loading from '@/app/loading'
import Link from 'next/link'
import { isEmpty, trim } from 'lodash'
import { GET_INVENTORIES } from '@/app/lib/graphQL/query'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsInventoryIcon } from '@/app/lib/icons/SealogsInventoryIcon'
import { InventoryFilterActions } from '@/components/filter/components/inventory-actions'
import { Badge, H4, P } from '@/components/ui'
import { cn } from '@/app/lib/utils'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

type BreakpointLimits = {
    desktop?: number
    laptop?: number
    landscape?: number
    'tablet-lg'?: number
    'tablet-md'?: number
    'tablet-sm'?: number
    phablet?: number
    standard?: number
    small?: number
    tiny?: number
}

// Component to display categories with overflow handling
const CategoryDisplay = ({
    categories,
    className,
    limits = { small: 2, landscape: 4 },
}: {
    categories: any
    className?: string
    limits?: BreakpointLimits
}) => {
    const bp = useBreakpoints()

    if (!categories?.nodes || categories.nodes.length === 0) {
        return null
    }

    // Determine current limit based on breakpoints (check from largest to smallest)
    const getCurrentLimit = (): number => {
        if (bp.desktop && limits.desktop !== undefined) return limits.desktop
        if (bp.laptop && limits.laptop !== undefined) return limits.laptop
        if (bp.landscape && limits.landscape !== undefined)
            return limits.landscape
        if (bp['tablet-lg'] && limits['tablet-lg'] !== undefined)
            return limits['tablet-lg']
        if (bp['tablet-md'] && limits['tablet-md'] !== undefined)
            return limits['tablet-md']
        if (bp['tablet-sm'] && limits['tablet-sm'] !== undefined)
            return limits['tablet-sm']
        if (bp.phablet && limits.phablet !== undefined) return limits.phablet
        if (bp.standard && limits.standard !== undefined) return limits.standard
        if (bp.small && limits.small !== undefined) return limits.small
        if (bp.tiny && limits.tiny !== undefined) return limits.tiny

        // Default fallback
        return 2
    }

    const currentLimit = getCurrentLimit()

    return (
        <div className="flex gap-2 items-center">
            {categories.nodes
                ?.slice(0, currentLimit)
                .map((cat: any, idx: number) => (
                    <Badge
                        key={String(idx)}
                        type="normal"
                        variant="outline"
                        className={cn('font-normal', className)}>
                        {cat.name}
                    </Badge>
                ))}
            {categories.nodes?.length > currentLimit && (
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            className={cn('font-normal', className)}>
                            + {categories.nodes.length - currentLimit} more
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80">
                        <div className="space-y-2">
                            <P className="font-medium text-sm">
                                All Categories
                            </P>
                            <div className="flex flex-wrap gap-2">
                                {categories.nodes.map(
                                    (cat: any, idx: number) => (
                                        <Badge
                                            key={String(idx)}
                                            type="normal"
                                            variant="outline"
                                            className="font-normal">
                                            {cat.name}
                                        </Badge>
                                    ),
                                )}
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            )}
        </div>
    )
}

export default function InventoryList() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const [inventories, setInventories] = useState<any[]>([])
    const [suppliers, setSuppliers] = useState<any>(null)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState<any[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [page, setPage] = useState(1)
    const [maxPage, setMaxPage] = useState(1)
    const limit = 20

    // Query inventories via GraphQL.
    const [queryInventories] = useLazyQuery(GET_INVENTORIES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventories.nodes
            if (data) {
                setInventories(data)
                setMaxPage(
                    Math.ceil(
                        response.readInventories.pageInfo.totalCount / limit,
                    ),
                )
            }
        },
        onError: (error: any) => {
            console.error('queryInventories error', error)
        },
    })

    // Load supplier data.
    getSupplier(setSuppliers)

    // Function to load inventories.
    const loadInventories = async (
        searchFilter: SearchFilter = {},
        searchkeywordFilter: any[] = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryInventories({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                            offset: (page - 1) * limit,
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            responses = responses.filter(
                (r: any) => r.data.readInventories.nodes.length > 0,
            )
            responses = responses.flatMap(
                (r: any) => r.data.readInventories.nodes,
            )
            responses = responses.filter(
                (value: any, index: number, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setInventories(responses)
        } else {
            setInventories([])
            await queryInventories({
                variables: {
                    filter: searchFilter,
                    offset: (page - 1) * limit,
                },
            })
        }
    }

    // Called when the Filter component changes.
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vesselID = { in: data.map((item) => +item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vesselID = { eq: +data.value }
            } else {
                delete searchFilter.vesselID
            }
        }
        if (type === 'supplier') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.suppliers = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.suppliers = { id: { in: [+data.value] } }
            } else {
                delete searchFilter.suppliers
            }
        }
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.categories = {
                    id: { eq: data.map((item) => String(item.value)) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.categories = { id: { eq: String(data.value) } }
            } else {
                delete searchFilter.categories
            }
        }
        if (type === 'keyword') {
            if (!isEmpty(trim(data?.value))) {
                setKeywordFilter([
                    { item: { contains: data.value } },
                    { title: { contains: data.value } },
                    { productCode: { contains: data.value } },
                    { description: { contains: data.value } },
                    { comments: { contains: data.value } },
                ])
            } else {
                setKeywordFilter([])
            }
        }
        setFilter(searchFilter)
        setPage(1)
        loadInventories(searchFilter, keywordFilter)
    }

    useEffect(() => {
        setPage(1)
        loadInventories(filter, keywordFilter)
        setIsLoading(false)
    }, [filter, page])

    useEffect(() => {
        loadInventories(filter, keywordFilter)
    }, [filter, keywordFilter])

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: 'Item',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="py-2.5 space-y-2">
                        <div>
                            <Link
                                href={`/inventory/view/?id=${inventory.id}&redirect_to=${pathname}?${searchParams.toString()}&tab=inventory`}
                                className="flex items-center">
                                {inventory.quantity + ' x ' + inventory.item}
                            </Link>
                            <div className="text-curious-blue-400 uppercase laptop:hidden text-[10px]">
                                {inventory.vessel?.title ||
                                    inventory.location ||
                                    'N/A'}
                            </div>
                        </div>
                        {inventory.suppliers?.nodes.length > 0 && (
                            <span className="flex gap-2">
                                <P>Sup:</P>
                                {inventory.suppliers?.nodes?.map(
                                    (supplier: any) => (
                                        <Link
                                            key={String(supplier.id)}
                                            href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                            {supplier.name}
                                        </Link>
                                    ),
                                )}
                            </span>
                        )}
                        {inventory.categories?.nodes.length > 0 && (
                            <span className="flex items-center gap-2">
                                <P>Cat:</P>
                                <CategoryDisplay
                                    className="py-1 px-2 h-fit text-sm"
                                    categories={inventory.categories}
                                />
                            </span>
                        )}
                    </div>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const text = (inventory.item || '').toLowerCase()
                return text.includes(filterValue.toLowerCase())
            },
        },
        {
            accessorKey: 'location',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div>
                        {inventory.vessel?.title || inventory.location || 'N/A'}
                    </div>
                )
            },
            cellAlignment: 'left',
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const loc = (
                    inventory.vessel?.title ||
                    inventory.location ||
                    ''
                ).toLowerCase()
                return loc.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.vessel?.title ||
                    rowA?.original?.location ||
                    ''
                const valueB =
                    rowB?.original?.vessel?.title ||
                    rowB?.original?.location ||
                    ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'maintenance',
            header: 'Maintenance',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original

                // Calculate maintenance status from componentMaintenanceChecks
                const getMaintenanceStatus = (inventory: any) => {
                    const checks =
                        inventory.componentMaintenanceChecks?.nodes || []

                    if (checks.length === 0) {
                        return null
                    }

                    // Filter active tasks (not archived)
                    const activeTasks = checks.filter(
                        (task: any) => !task?.archived,
                    )

                    // Count overdue tasks using the same logic as inventory view
                    const overdueTasks = activeTasks.filter((task: any) => {
                        const overDueInfo = isOverDueTask(task)
                        const isOverdue = overDueInfo.status === 'High'

                        return isOverdue
                    })

                    if (overdueTasks.length > 0) {
                        return { type: 'overdue', count: overdueTasks.length }
                    }

                    // If there are maintenance checks but none are overdue, show good status
                    return { type: 'good' }
                }

                const maintenanceStatus = getMaintenanceStatus(inventory) || {
                    type: 'good',
                }
                return (
                    <>
                        {maintenanceStatus?.type === 'overdue' ? (
                            <Badge variant="destructive">
                                {maintenanceStatus.count}
                            </Badge>
                        ) : maintenanceStatus?.type === 'good' ? (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        ) : null}
                    </>
                )
            },
        },
        {
            accessorKey: 'categories',
            header: 'Categories',
            cellAlignment: 'left',
            breakpoint: 'landscape',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return <CategoryDisplay categories={inventory.categories} />
            },
        },
        {
            accessorKey: 'suppliers',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Suppliers" />
            ),
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <>
                        {inventory.suppliers?.nodes?.map((supplier: any) => (
                            <Link
                                key={String(supplier.id)}
                                href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                {supplier.name}
                            </Link>
                        ))}
                    </>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                if (!filterValue) return true
                const supplierNames = (inventory.suppliers?.nodes || [])
                    .map((s: any) => s.name.toLowerCase())
                    .join(' ')
                return supplierNames.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.suppliers?.nodes?.[0]?.name || ''
                const valueB = rowB?.original?.suppliers?.nodes?.[0]?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={
                    <SealogsInventoryIcon
                        className={`h-12 w-12 ring-1 bg-curious-blue-50 p-1 rounded-full`}
                    />
                }
                title="All inventory"
                actions={<InventoryFilterActions />}
            />
            <div className="mt-16">
                {isLoading ? (
                    <Loading />
                ) : (
                    <DataTable
                        columns={columns}
                        data={inventories}
                        showToolbar={true}
                        pageSize={limit}
                        onChange={handleFilterOnChange}
                    />
                )}
            </div>
        </>
    )
}
